import * as vscode from 'vscode';
import * as child_process from 'child_process';
import * as path from 'path';
import { outputChannel, getFunctionCodeByLine, getConfiguration } from './UtilFuns';

import OpenAI from "openai";


//apiKey: '7MCzS52Jaq6vDXvD'
async function requestAI(prompt: string): Promise<string | null> {
  const openai = new OpenAI({
        baseURL: 'https://llmapi.horizon.auto/v1',
        apiKey: 'q4ALyiSODlAjMm8o'
  });

  // Get the AI model from configuration
  const aiModel = getConfiguration().get("aiModel") as string || "DeepSeek-V3";
  const temp = getConfiguration().get("aiTemperature") as number || 0.2;
  reviewOutputChannel.appendLine(`Using model ${aiModel} `);
  const completion = await openai.chat.completions.create({
    messages: [{ role: "system", content: prompt }],
    model: aiModel,
    temperature: temp
  });
  return completion.choices[0].message.content ?? null;
}

// Create a dedicated output channel for code reviews
const reviewOutputChannel = vscode.window.createOutputChannel('SourceSeek Code Review');

/**
 * Review the current file by sending its content to AI for analysis
 */
export async function reviewCurrentFile(useRule: boolean = false, customRuleFile: string = ""): Promise<void> {
    try {
        // Get the active editor
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor');
            return;
        }

        // Get the current file path and content
        const currentFilePath = editor.document.uri.fsPath;
        const fileName = path.basename(currentFilePath);
        const fileContent = editor.document.getText();
        
        // Show a progress notification
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Analyzing current file...',
            cancellable: true
        }, async (progress) => {
            // Clear the output channel
            reviewOutputChannel.clear();
            reviewOutputChannel.appendLine(`Analyzing file: ${fileName}\n`);
            
            try {
                // Create the prompt for AI analysis
                let prompt = "";
                
                if (useRule) {
                    try {
                        let rulesFilePath = "";
                        if (customRuleFile) {
                            // Use the custom rule file path
                            rulesFilePath = customRuleFile;
                            reviewOutputChannel.appendLine(`Using custom rule file: ${customRuleFile}`);
                        } else {
                            // Get the path to the c_rules.md file in the extension directory
                            const extensionPath = vscode.extensions.getExtension('lingxf.SourceSeek')?.extensionPath || '';
                            rulesFilePath = path.join(extensionPath, 'rules/c_rules.md');
                            reviewOutputChannel.appendLine(`Using built-in rule file: ${rulesFilePath}`);
                        }
                        // Read the rules file
                        const rulesFileContent = await vscode.workspace.fs.readFile(vscode.Uri.file(rulesFilePath));
                        const rules = Buffer.from(rulesFileContent).toString('utf8');
                        
                        // Include rules in the prompt
                        const link = `(vscode://file/${currentFilePath}:10)`;
                        // Get the current line number
                        const currentLine = editor.selection.active.line + 1; // 1-based line number
                        // Create a link to the current file at the current line
                        const lineLink = `[${fileName}:${currentLine}](vscode://file/${currentFilePath}:${currentLine})`;
                        reviewOutputChannel.appendLine(`Current position: ${lineLink}`);

                        prompt = `请根据以下编码规则和最佳实践，分析并审查代码文件，指出潜在的问题和改进建议(用中文回答), 潜在问题和建议的代码位置附上markdown格式vscode文件链接(类似${link}):\n\n规则说明：\n${rules}\n\n代码文件：\n${fileContent}\n\n`;
                        reviewOutputChannel.appendLine('Including coding rules in analysis...\n');
                    } catch (error) {
                        // Fall back to standard prompt if rules file can't be read
                        reviewOutputChannel.appendLine(`Warning: Could not read rules file. Using standard prompt instead.\n`);
                        prompt = `请分析并审查以下代码文件，指出可能的问题、潜在的改进、代码结构和最佳实践的遵循情况(用中文回答):\n\n${fileContent}`;
                    }
                } else {
                    prompt = `请分析并审查以下代码文件，指出可能的问题、潜在的改进、代码结构和最佳实践的遵循情况(用中文回答):\n\n${fileContent}`;
                }
                
                // Send to AI for analysis
                reviewOutputChannel.appendLine('Sending file to AI for analysis...');
                const startTime = Date.now();
                const analysis = await requestAI(prompt);
                const endTime = Date.now();
                const timeCost = ((endTime - startTime) / 1000).toFixed(2);

                if (analysis) {
                    // Display the AI analysis
                    reviewOutputChannel.appendLine('AI Analysis:');
                    reviewOutputChannel.appendLine('----------------------------------------\n');
                    reviewOutputChannel.appendLine(analysis);
                    reviewOutputChannel.appendLine('\n----------------------------------------');
                } else {
                    reviewOutputChannel.appendLine('No analysis received from AI.');
                }
                reviewOutputChannel.appendLine(`\n[AI request time cost: ${timeCost} seconds]`);
                
                // Show the output channel
                reviewOutputChannel.show(true);
            } catch (error) {
                reviewOutputChannel.appendLine(`Error analyzing file: ${error instanceof Error ? error.message : String(error)}`);
                reviewOutputChannel.show(true);
            }
        });
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to analyze file: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Review code changes using git diff and display the results in the output channel
 */
export async function reviewCodeChanges(command: string, fileOnly: boolean = true): Promise<void> {
    try {
        // Get the active editor
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor');
            return;
        }

        const ruleFile = await selectRuleFileFromExtension();
        let rules = '';
        if (ruleFile != '') {
            const rulesFileContent = await vscode.workspace.fs.readFile(vscode.Uri.file(ruleFile));
            rules = Buffer.from(rulesFileContent).toString('utf8');
        } else {
            rules = '';
        }

        // Get the current file path and its directory
        const currentFilePath = editor.document.uri.fsPath;
        const fileDirectory = path.dirname(currentFilePath);
        const fileName = path.basename(currentFilePath);

        try {
            let statusCommand = `git status --porcelain`;
            if (fileOnly) {
                statusCommand = statusCommand + ' ' + fileName;
            }
            const statusResult = await executeCommand(statusCommand, fileDirectory);

            if (statusResult.trim()) {
                reviewOutputChannel.appendLine('\nFile status:');
                reviewOutputChannel.appendLine(statusResult);
            }
        } catch (error) {
            // Just log the error but don't show it to the user
            outputChannel.appendLine(`Error getting git status: ${error instanceof Error ? error.message : String(error)}`);
        }

        if (fileOnly) {
            command = command + ' ' + fileName;
        }

        // Show a progress notification
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Reviewing code changes...',
            cancellable: true
        }, async (progress) => {
            // Clear the output channel
            reviewOutputChannel.clear();
            if (fileOnly) {
                reviewOutputChannel.appendLine(`Reviewing changes for: ${fileName}`);
            } 

            if (ruleFile != '') {
                reviewOutputChannel.appendLine(`Using rules: ${ruleFile}`);
            }
            reviewOutputChannel.appendLine(`Working directory: ${fileDirectory}`);
            
            // Run git command on the current file
            reviewOutputChannel.appendLine(`Executing: ${command}`);
            
            try {
                // Execute git command using file's directory as working directory
                const result = await executeCommand(command, fileDirectory);
                reviewOutputChannel.appendLine(result);
                reviewOutputChannel.appendLine('\n--- End of git command output ---\n');
                
                if (result.trim()) {
                    // Display the git output
                    reviewOutputChannel.appendLine(result);
                    const filecontext = editor.document.getText();
                    let prompt = `请根据补丁和原始文件，给出详细的审查意见，包括潜在的问题、改进建议和代码结构等方面的分析, 输出格式为markdown格式，有问题代码包含代码链接。\n`;
                    prompt += `规则要求:\n${rules}\n`;
                    prompt += `补丁是:\n${result}\n`;
                    if (fileOnly) {
                        prompt += `原始文件是:\n${filecontext}`;
                    }
                    //reviewOutputChannel.appendLine(prompt);
                    const startTime = Date.now();
                    const answer = await requestAI(prompt);
                    const endTime = Date.now();
                    const timeCost = ((endTime - startTime) / 1000).toFixed(2);
                    if (answer) {
                        reviewOutputChannel.appendLine(answer);
                    }
                    reviewOutputChannel.appendLine(`\n[AI request time cost: ${timeCost} seconds]`);
                } else {
                    reviewOutputChannel.appendLine('No output from git command for this file.');
                }
                
                // Show the output channel
                reviewOutputChannel.show(true);
            } catch (error) {
                reviewOutputChannel.appendLine(`Error executing ${command}: ${error instanceof Error ? error.message : String(error)}`);
                reviewOutputChannel.show(true);
            }

            // Also try to show git status for the file

        });
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to review code changes: ${error instanceof Error ? error.message : String(error)}`);
    }
}

export async function reviewDiffs(): Promise<void> {
    const command = 'git diff';
    await reviewCodeChanges(command);
}

/**
 * Review the last commit using git show
 */
export async function reviewLastCommit(): Promise<void> {
    const gitShowCommand = `git log -p -1 --color=never`;
    await reviewCodeChanges(gitShowCommand);
}

export async function reviewLastGitCommit(): Promise<void> {
    const gitShowCommand = `git show --color=never`;
    await reviewCodeChanges(gitShowCommand, false);
}

export async function reviewWholeGitDiff(): Promise<void> {
    const gitShowCommand = `git diff --color=never`;
    await reviewCodeChanges(gitShowCommand, false);
}


/**
 * Execute a shell command and return the output
 * @param command The command to execute
 * @param cwd The working directory
 * @returns The command output
 */
function executeCommand(command: string, cwd: string): Promise<string> {
    return new Promise((resolve, reject) => {
        child_process.exec(command, { cwd }, (error, stdout, stderr) => {
            if (error && stderr) {
                // Some git commands return error code but still produce useful output
                if (stdout) {
                    resolve(stdout);
                } else {
                    reject(new Error(stderr));
                }
            } else {
                resolve(stdout);
            }
        });
    });
}

/**
 * Review the current file with rules by sending its content to AI for analysis with rule reference
 */
export async function reviewCurrentFileWithRule(): Promise<void> {
    const ruleFile = await selectRuleFileFromExtension();
    await reviewCurrentFile(true, ruleFile);
}

export async function reviewCurrentFileWithCustomRule(): Promise<void> {
    const customRuleFile = await getCustomerRuleFile();
    await reviewCurrentFile(true, customRuleFile);
}
/**
 * List and select a rule file from the extension directory
 * @returns Promise<string> Path to the selected rule file or empty string if cancelled
 */
export async function selectRuleFileFromExtension(): Promise<string> {
    try {
        // Get the extension path
        const extensionPath = vscode.extensions.getExtension('lingxf.SourceSeek')?.extensionPath;
        if (!extensionPath) {
            throw new Error('Could not find extension path');
        }

        // Read all files in the extension directory
        const rule_folder = path.join(extensionPath, 'rules');
        const files = await vscode.workspace.fs.readDirectory(vscode.Uri.file(rule_folder));
        
        // Filter for *_rules.md files
        const ruleFiles = files
            .filter(([name, type]) => type === vscode.FileType.File && (name.endsWith('_rules.md') || name.includes('_rules_')))
            .map(([name]) => name);

        ruleFiles.push('Customer Rules');
        if (ruleFiles.length === 0) {
            vscode.window.showInformationMessage('No rule files found in extension directory');
            return '';
        }

        // Show quick pick to select a rule file
        const selected = await vscode.window.showQuickPick(ruleFiles, {
            placeHolder: 'Select a rule file',
            canPickMany: false
        });

        if (selected) {
            if (selected === 'Customer Rules') {
                return getCustomerRuleFile();
            }
            return path.join(rule_folder, selected);
        }

        return '';
    } catch (error) {
        vscode.window.showErrorMessage(`Error listing rule files: ${error instanceof Error ? error.message : String(error)}`);
        return '';
    }
}

/**
 * Open a rule file in the editor
 */
export async function openRuleFile(): Promise<void> {
    try {
        // Get the rule file path
        const ruleFilePath = await selectRuleFileFromExtension();
        if (!ruleFilePath) {
            return;
        }

        // Open the rule file in the editor
        const ruleFileUri = vscode.Uri.file(ruleFilePath);
        await vscode.commands.executeCommand('vscode.open', ruleFileUri);
        
        // vscode.window.showInformationMessage(`Opened rule file: ${path.basename(ruleFilePath)}`);
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to open rule file: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Review the current file with custom rules specified in the user settings
 */
export async function getCustomerRuleFile(): Promise<string> {
    // Get the custom rule file path from settings
    const config = vscode.workspace.getConfiguration('sourceseek');
    let customRuleFile = config.get<string>('customRuleFile') || '';
    
    // If no custom rule file is set, prompt the user to select one
    if (!customRuleFile) {
            const fileUri = await vscode.window.showOpenDialog({
                canSelectMany: false,
                filters: {
                    'Markdown Files': ['md'],
                    'Text Files': ['txt'],
                    'All Files': ['*']
                },
                title: 'Select Custom Rule File'
            });
            
            if (fileUri && fileUri.length > 0) {
                customRuleFile = fileUri[0].fsPath;
                
                // Ask if the user wants to save this path for future use
                const saveResult = await vscode.window.showInformationMessage(
                    'Would you like to save this rule file path for future use?',
                    'Yes',
                    'No, Just Use Once'
                );
                
                if (saveResult === 'Yes') {
                    // Save the path to settings
                    await config.update('customRuleFile', customRuleFile, vscode.ConfigurationTarget.Global);
                    vscode.window.showInformationMessage(`Custom rule file path saved: ${customRuleFile}`);
                }
            } else {
                // User cancelled the file selection
                vscode.window.showInformationMessage('Using built-in rules instead.');
                await reviewCurrentFileWithRule();
                return '';
            }
    }
    return customRuleFile;
}


/**
 * Review the current function at cursor position
 */
export async function reviewCurrentFunction(): Promise<void> {
    try {
        // Get the active editor
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor');
            return;
        }

        // Get the current cursor position
        const cursorPosition = editor.selection.active;
        const currentLineNumber = cursorPosition.line;
        
        // Extract the function at current cursor position
        const functionCodeAndLine  = await getFunctionCodeByLine(editor.document, currentLineNumber);
        
        if (!functionCodeAndLine) {
            vscode.window.showWarningMessage('Could not identify a function at the current cursor position');
            return;
        }

        const ruleFile = await selectRuleFileFromExtension();
        let rules = '';
        if (ruleFile != '') {
            const rulesFileContent = await vscode.workspace.fs.readFile(vscode.Uri.file(ruleFile));
            rules = Buffer.from(rulesFileContent).toString('utf8');
        } else {
            rules = '';
        } 
       
        // Get file information
        const currentFilePath = editor.document.uri.fsPath;
        const fileName = path.basename(currentFilePath);
        
        // Show a progress notification
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Analyzing current function...',
            cancellable: true
        }, async (progress) => {
            // Clear the output channel
            reviewOutputChannel.clear();
            reviewOutputChannel.appendLine(`Analyzing function at ${fileName}:${functionCodeAndLine.startLine}\n`);
            reviewOutputChannel.appendLine(`Using Rulefile: ${ruleFile}\n`);
            
            try {
                // Create a link to the current file at the function start line
                const lineLink = `[${fileName}:${functionCodeAndLine.startLine}](vscode://file/${currentFilePath}:${functionCodeAndLine.startLine})`;
                reviewOutputChannel.appendLine(`Function location: ${lineLink}`);
                
                // Create the prompt for AI analysis
                let prompt = `请分析并审查以下代码函数，重点关注函数的结构、逻辑、潜在的问题或优化机会。请提供具体的建议来提高代码质量和可维护性(用中文回答):\n\n`
                prompt += `函数代码：\n\n ${functionCodeAndLine.code}`;
                if (rules != '')
                    prompt +=  `规则要求：\n\n ${rules}`;

                
                // Send to AI for analysis
                reviewOutputChannel.appendLine('Sending function to AI for analysis...\n');
                const analysis = await requestAI(prompt);
                
                if (analysis) {
                    // Display the AI analysis
                    reviewOutputChannel.appendLine('AI Analysis:');
                    reviewOutputChannel.appendLine('----------------------------------------\n');
                    reviewOutputChannel.appendLine(analysis);
                    reviewOutputChannel.appendLine('\n----------------------------------------');
                } else {
                    reviewOutputChannel.appendLine('No analysis received from AI.');
                }
                
                // Show the output channel
                reviewOutputChannel.show(true);
            } catch (error) {
                reviewOutputChannel.appendLine(`Error analyzing function: ${error instanceof Error ? error.message : String(error)}`);
                reviewOutputChannel.show(true);
            }
        });
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to analyze function: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Register the review code command
 * @param context The extension context
 */
export function registerReviewCodeCommand(context: vscode.ExtensionContext): void {
    // Register the commands
    const disposableReviewChanges = vscode.commands.registerCommand('sourceseek.reviewDiffs', reviewDiffs);
    const disposableReviewLastCommit = vscode.commands.registerCommand('sourceseek.reviewLastCommit', reviewLastCommit);
    const disposableReviewCurrentFile = vscode.commands.registerCommand('sourceseek.reviewCurrentFile', reviewCurrentFileWithRule);
    const disposableReviewCurrentFileWithCustomRule = vscode.commands.registerCommand('sourceseek.reviewCurrentFileWithCustomerRule', reviewCurrentFileWithCustomRule);
    const disposableReviewCurrentFunction = vscode.commands.registerCommand('sourceseek.reviewCurrentFunction', reviewCurrentFunction);
    const disposableReviewWholeGitDiff = vscode.commands.registerCommand('sourceseek.reviewWholeGitDiff', reviewWholeGitDiff);
    const disposableReviewLastGitCommit = vscode.commands.registerCommand('sourceseek.reviewLastGitCommit', reviewLastGitCommit);
    const disposableOpenRule = vscode.commands.registerCommand('sourceseek.openRule', openRuleFile);
    
    context.subscriptions.push(disposableReviewChanges);
    context.subscriptions.push(disposableReviewLastCommit);
    context.subscriptions.push(disposableReviewCurrentFile);
    context.subscriptions.push(disposableReviewCurrentFileWithCustomRule);
    context.subscriptions.push(disposableReviewCurrentFunction);
    context.subscriptions.push(disposableReviewWholeGitDiff);
    context.subscriptions.push(disposableReviewLastGitCommit);
    context.subscriptions.push(disposableOpenRule);
} 