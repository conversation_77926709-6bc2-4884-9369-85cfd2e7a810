import * as vscode from 'vscode';
import * as path from 'path';
import { findCallers, SymbolInfo } from './CallHierarchyProvider';
import { findFunctionNameByLine, outputChannel, getFullPath } from './UtilFuns';

interface CallerNode {
    name: string;
    filePath: string;
    lineNumbers: number[];
    description: string;
}

export function registerShowCallerCommand(context: vscode.ExtensionContext): void {
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.showCaller', async () => {
            try {
                const editor = vscode.window.activeTextEditor;
                if (!editor) {
                    vscode.window.showErrorMessage('No active editor found');
                    return;
                }

                // Get the function name under the cursor
                const position = editor.selection.active;
                const document = editor.document;
                const range = document.getWordRangeAtPosition(position);
                
                if (!range) {
                    vscode.window.showErrorMessage('No word found under cursor');
                    return;
                }

                const functionName = document.getText(range);
                
                // Check if current file is C/C++
                const fileName = document.fileName.toLowerCase();
                const isCpp = fileName.endsWith('.cpp') || fileName.endsWith('.cc') ||
                    fileName.endsWith('.cxx') || fileName.endsWith('.hpp') ||
                    fileName.endsWith('.h++');
                const isC = fileName.endsWith('.c') || fileName.endsWith('.h');

                if (!isCpp && !isC) {
                    vscode.window.showErrorMessage('Show Caller is only supported for C/C++ files');
                    return;
                }

                // Show progress
                await vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: `Finding callers for ${functionName}...`,
                    cancellable: false
                }, async (progress) => {
                    try {
                        // Find all callers
                        const callers = await findCallers(functionName, isCpp);
                        
                        if (callers.length === 0) {
                            vscode.window.showInformationMessage(`No callers found for function: ${functionName}`);
                            return;
                        }

                        // Group callers by function name and collect line numbers
                        const callerMap = new Map<string, CallerNode>();
                        
                        for (const caller of callers) {
                            const key = `${caller.name}:${caller.filePath}`;
                            if (callerMap.has(key)) {
                                const existing = callerMap.get(key)!;
                                existing.lineNumbers.push(caller.linePosition);
                            } else {
                                callerMap.set(key, {
                                    name: caller.name,
                                    filePath: caller.filePath,
                                    lineNumbers: [caller.linePosition],
                                    description: caller.description || ''
                                });
                            }
                        }

                        // Create and show the webview
                        const panel = vscode.window.createWebviewPanel(
                            'callerFlow',
                            `Caller Flow: ${functionName}`,
                            vscode.ViewColumn.One,
                            {
                                enableScripts: true,
                                retainContextWhenHidden: true
                            }
                        );

                        // Set the HTML content
                        panel.webview.html = getWebviewContent(functionName, Array.from(callerMap.values()));

                        // Handle messages from the webview
                        panel.webview.onDidReceiveMessage(
                            async (message) => {
                                switch (message.command) {
                                    case 'openFile':
                                        try {
                                            // Resolve the full path
                                            const fullPath = getFullPath(message.filePath);
                                            const uri = vscode.Uri.file(fullPath);
                                            const doc = await vscode.workspace.openTextDocument(uri);
                                            const editor = await vscode.window.showTextDocument(doc);

                                            // Jump to the specific line
                                            const line = message.lineNumber - 1; // Convert to 0-based
                                            const position = new vscode.Position(line, 0);
                                            editor.selection = new vscode.Selection(position, position);
                                            editor.revealRange(new vscode.Range(position, position));
                                        } catch (error) {
                                            vscode.window.showErrorMessage(`Failed to open file: ${error}`);
                                        }
                                        break;
                                }
                            },
                            undefined,
                            context.subscriptions
                        );

                    } catch (error) {
                        outputChannel.appendLine(`Error finding callers: ${error}`);
                        vscode.window.showErrorMessage(`Error finding callers: ${error}`);
                    }
                });

            } catch (error) {
                outputChannel.appendLine(`Error in showCaller command: ${error}`);
                vscode.window.showErrorMessage(`Error: ${error}`);
            }
        })
    );
}

function getWebviewContent(childFunction: string, callers: CallerNode[]): string {
    const callerElements = callers.map((caller, index) => {
        const lineNumbersText = caller.lineNumbers.sort((a, b) => a - b).join(', ');
        const fileName = path.basename(caller.filePath);

        return `
            <div class="caller-node" id="caller-${index}">
                <div class="function-name">${escapeHtml(caller.name)}</div>
                <div class="file-info">${escapeHtml(fileName)}</div>
                <div class="line-numbers">Lines: ${lineNumbersText}</div>
                <div class="line-buttons">
                    ${caller.lineNumbers.map(line =>
                        `<button class="line-button" onclick="openFile('${escapeHtml(caller.filePath)}', ${line})">Line ${line}</button>`
                    ).join('')}
                </div>
            </div>
            <div class="arrow" id="arrow-${index}">
                <svg width="100" height="50">
                    <defs>
                        <marker id="arrowhead-${index}" markerWidth="10" markerHeight="7"
                                refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#007acc" />
                        </marker>
                    </defs>
                    <line x1="10" y1="25" x2="90" y2="25" stroke="#007acc" stroke-width="2"
                          marker-end="url(#arrowhead-${index})" />
                </svg>
            </div>
        `;
    }).join('');

    // Helper function to escape HTML
    function escapeHtml(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    return `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Caller Flow</title>
        <style>
            body {
                font-family: var(--vscode-font-family);
                padding: 20px;
                color: var(--vscode-foreground);
                background-color: var(--vscode-editor-background);
                margin: 0;
            }
            .container {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 20px;
                max-width: 1200px;
                margin: 0 auto;
            }
            .title {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 20px;
                text-align: center;
            }
            .flow-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 10px;
                width: 100%;
            }
            .caller-node {
                background-color: var(--vscode-input-background);
                border: 2px solid var(--vscode-input-border);
                border-radius: 8px;
                padding: 15px;
                min-width: 250px;
                max-width: 400px;
                text-align: center;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                transition: all 0.3s ease;
            }
            .caller-node:hover {
                border-color: var(--vscode-focusBorder);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }
            .function-name {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 8px;
                color: var(--vscode-symbolIcon-functionForeground);
            }
            .file-info {
                font-size: 14px;
                color: var(--vscode-descriptionForeground);
                margin-bottom: 8px;
            }
            .line-numbers {
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
                margin-bottom: 8px;
            }
            .line-buttons {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                justify-content: center;
            }
            .line-button {
                background-color: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
                transition: all 0.2s ease;
                min-width: 50px;
            }
            .line-button:hover {
                background-color: var(--vscode-button-hoverBackground);
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            .arrow {
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .child-function {
                background-color: var(--vscode-editor-selectionBackground);
                border: 3px solid var(--vscode-focusBorder);
                border-radius: 8px;
                padding: 20px;
                min-width: 250px;
                text-align: center;
                font-size: 20px;
                font-weight: bold;
                color: var(--vscode-symbolIcon-functionForeground);
                box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            }
            .no-callers {
                text-align: center;
                color: var(--vscode-descriptionForeground);
                font-style: italic;
                margin: 40px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="title">Caller Flow Diagram</div>
            
            <div class="flow-container">
                ${callers.length > 0 ? callerElements : '<div class="no-callers">No callers found</div>'}
                
                <div class="child-function">
                    ${childFunction}
                    <div style="font-size: 14px; font-weight: normal; margin-top: 5px; color: var(--vscode-descriptionForeground);">
                        (Target Function)
                    </div>
                </div>
            </div>
        </div>

        <script>
            const vscode = acquireVsCodeApi();
            
            function openFile(filePath, lineNumber) {
                vscode.postMessage({
                    command: 'openFile',
                    filePath: filePath,
                    lineNumber: lineNumber
                });
            }
        </script>
    </body>
    </html>`;
}
